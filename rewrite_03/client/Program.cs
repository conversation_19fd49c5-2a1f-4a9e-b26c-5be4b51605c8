using System.Text.Json;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddHttpClient("default").ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
    };
});
builder.Services.AddCors();

var app = builder.Build();

app.UseCors(policy => policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

const string AI_API_URL = "https://api.epicai.fun/v1/chat/completions";
const string AI_API_KEY = "ecs-00";
const string MCP_SERVER_URL = "http://localhost:5001";

app.MapPost("/ask", async (AskRequest request, IHttpClientFactory httpClientFactory) =>
{
    var httpClient = httpClientFactory.CreateClient("default");
    
    if (string.IsNullOrEmpty(request.Message))
    {
        return Results.BadRequest(new { error = "Message is required" });
    }
    
    try
    {
        // Step 1: Get available tools from MCP server
        var toolsResponse = await httpClient.GetStringAsync($"{MCP_SERVER_URL}/tools");
        var tools = JsonSerializer.Deserialize<Tool[]>(toolsResponse) ?? Array.Empty<Tool>();

        // Step 2: Ask AI which tools to use
        var toolSelectionPrompt = $@"
User message: {request.Message}

Available tools:
{string.Join("\n", tools.Select(t => $"- {t.Name}: {t.Description}"))}

Determine which tools (if any) are needed to answer the user's question.
Respond with ONLY a JSON array of tool names. Examples:
- For ""hello"": []
- For ""how many files"": [""getVaultFiles""]
- For ""files modified last week"": [""getTime"", ""getVaultFiles""]
- For ""search for dwg"": [""searchVaultFiles""]

JSON array only:";

        var toolSelectionResponse = await CallAI(httpClient, toolSelectionPrompt);

        // Step 3: Parse tool selection and execute tools
        var toolResults = new List<object>();
        try
        {
            var selectedTools = JsonSerializer.Deserialize<string[]>(toolSelectionResponse.Trim()) ?? Array.Empty<string>();

            foreach (var toolName in selectedTools)
            {
                var toolParams = new Dictionary<string, object>();

                // Add default params based on tool
                if (toolName == "getVaultFiles" || toolName == "searchVaultFiles")
                {
                    toolParams["vaultId"] = "117";
                }

                if (toolName == "searchVaultFiles")
                {
                    // Extract search query from user message
                    var searchQuery = ExtractSearchQuery(request.Message);
                    toolParams["query"] = searchQuery;
                }

                var toolRequest = new { tool = toolName, @params = toolParams };
                var toolJson = JsonSerializer.Serialize(toolRequest);
                var content = new StringContent(toolJson, Encoding.UTF8, "application/json");

                var toolResponse = await httpClient.PostAsync($"{MCP_SERVER_URL}/tool", content);
                var toolResult = await toolResponse.Content.ReadAsStringAsync();
                var deserializedResult = JsonSerializer.Deserialize<object>(toolResult);
                if (deserializedResult != null)
                    toolResults.Add(deserializedResult);
            }
        }
        catch
        {
            // If tool selection parsing fails, continue without tools
        }

        // Step 4: AI summarizes results and responds
        var finalPrompt = $@"
User asked: {request.Message}

Tool execution results: {JsonSerializer.Serialize(toolResults)}

Based on the tool results, provide a helpful and natural response to the user's question. Be concise and friendly.
If no tool results are available, provide a general helpful response.
";

        var finalResponse = await CallAI(httpClient, finalPrompt);

        return Results.Ok(new { reply = finalResponse });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

app.MapGet("/health", () => new { status = "ok", service = "mcp-client" });

app.Run("http://localhost:5098");

string ExtractSearchQuery(string message)
{
    // Simple extraction of search terms
    var lowerMessage = message.ToLower();

    // Look for patterns like "search for X", "find X", etc.
    var patterns = new[]
    {
        @"search.*?for\s+(.+)",
        @"find\s+(.+)",
        @"look.*?for\s+(.+)",
        @"show.*?me\s+(.+)"
    };

    foreach (var pattern in patterns)
    {
        var match = System.Text.RegularExpressions.Regex.Match(lowerMessage, pattern);
        if (match.Success && match.Groups.Count > 1)
        {
            return match.Groups[1].Value.Trim();
        }
    }

    // If no pattern matches, return the whole message as search query
    return message;
}

async Task<string> CallAI(HttpClient httpClient, string prompt)
{
    try
    {
        var aiRequest = new
        {
            model = "gpt-4.1-nano",
            messages = new[]
            {
                new { role = "user", content = prompt }
            },
            temperature = 0.1,
            max_tokens = 1000,
            stream = false  // Disable streaming
        };

        var json = JsonSerializer.Serialize(aiRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        httpClient.DefaultRequestHeaders.Clear();
        httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {AI_API_KEY}");

        var response = await httpClient.PostAsync(AI_API_URL, content);
        var responseJson = await response.Content.ReadAsStringAsync();

        Console.WriteLine($"AI API Response: {responseJson}");

        var aiResponse = JsonSerializer.Deserialize<AiResponse>(responseJson);

        return aiResponse?.Choices?[0]?.message?.content ?? "No response from AI";
    }
    catch (Exception ex)
    {
        Console.WriteLine($"AI Call Error: {ex.Message}");
        return "Sorry, I encountered an error while processing your request.";
    }
}

public record AskRequest(string Message);
public record Tool(string Name, string Description);
public record ToolCall(string Tool, Dictionary<string, object> Params);
public record AiResponse(Choice[] Choices);
public record Choice(Message Message);
public record Message(string Content);
