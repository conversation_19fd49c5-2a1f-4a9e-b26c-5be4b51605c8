<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vault MCP AI - Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-section {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus {
            border-color: #4CAF50;
            outline: none;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .response-section {
            margin-top: 30px;
        }
        .response {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            border-color: #f8bbd9;
        }
        .examples {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e8;
            border-radius: 5px;
        }
        .examples h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .examples ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .examples li {
            margin: 5px 0;
            cursor: pointer;
            color: #1976d2;
        }
        .examples li:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Vault MCP AI Assistant</h1>
        
        <div class="input-section">
            <label for="messageInput">Ask about your vault files:</label>
            <input type="text" id="messageInput" placeholder="e.g., 'list my files', 'search for dwg files', 'files by TestAccount'" />
            <button onclick="sendMessage()" id="sendButton">Ask AI</button>
        </div>

        <div class="examples">
            <h3>Example Questions:</h3>
            <ul>
                <li onclick="setMessage('list my files')">📋 List my files</li>
                <li onclick="setMessage('search for ipt files')">🔍 Search for ipt files</li>
                <li onclick="setMessage('files created by TestAccount')">👤 Files created by TestAccount</li>
                <li onclick="setMessage('show me released files')">✅ Show me released files</li>
                <li onclick="setMessage('documentation files')">📚 Documentation files</li>
                <li onclick="setMessage('files in work in progress state')">⚠️ Files in work in progress state</li>
            </ul>
        </div>

        <div class="response-section">
            <label>AI Response:</label>
            <div id="response" class="response">Ready to answer your vault questions...</div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:5099/ask';

        function setMessage(message) {
            document.getElementById('messageInput').value = message;
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const responseDiv = document.getElementById('response');
            const sendButton = document.getElementById('sendButton');
            
            const message = messageInput.value.trim();
            if (!message) {
                alert('Please enter a message');
                return;
            }

            sendButton.disabled = true;
            sendButton.textContent = 'Processing...';
            responseDiv.className = 'response loading';
            responseDiv.textContent = 'AI is analyzing your vault question...';

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                responseDiv.className = 'response';
                responseDiv.textContent = data.reply || 'No response received';
                
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.textContent = `Error: ${error.message}\n\nMake sure the MCP client is running on localhost:5099`;
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = 'Ask AI';
            }
        }

        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
