const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = 5002;
const VAULT_API_BASE = 'http://localhost:4000';

app.use(cors());
app.use(express.json());

const tools = [
  {
    name: "listVaultFiles",
    description: "List all files in the vault with optional filtering and search"
  },
  {
    name: "searchVaultFiles", 
    description: "Search for specific files in the vault by name or other criteria"
  },
  {
    name: "getVaultFilesByUser",
    description: "Get files created or checked out by a specific user"
  },
  {
    name: "getVaultFilesByState",
    description: "Get files filtered by their state (e.g., 'Released', 'Work in Progress')"
  },
  {
    name: "getVaultFilesByCategory",
    description: "Get files filtered by category (e.g., 'Base', 'Documentation')"
  }
];

app.get('/tools', (req, res) => {
  res.json(tools);
});

app.post('/tool', async (req, res) => {
  const { tool, params } = req.body;
  
  try {
    let result;
    const vaultId = params?.vaultId || '117';
    
    switch (tool) {
      case 'listVaultFiles':
        result = await getVaultFiles(vaultId, params);
        break;

      case 'searchVaultFiles':
        const query = params?.query || '';
        result = await searchVaultFiles(vaultId, query, params);
        break;

      case 'getVaultFilesByUser':
        const userName = params?.userName || '';
        result = await getVaultFilesByUser(vaultId, userName, params);
        break;

      case 'getVaultFilesByState':
        const state = params?.state || '';
        result = await getVaultFilesByState(vaultId, state, params);
        break;

      case 'getVaultFilesByCategory':
        const category = params?.category || '';
        result = await getVaultFilesByCategory(vaultId, category, params);
        break;
        
      default:
        return res.status(400).json({ success: false, error: 'Unknown tool' });
    }
    
    res.json({ success: true, result });
  } catch (error) {
    console.error('Tool execution error:', error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

async function getVaultFiles(vaultId, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.latestOnly !== undefined) queryParams.append('option[latestOnly]', params.latestOnly);
    if (params.releasedFilesOnly) queryParams.append('option[releasedFilesOnly]', 'true');
    
    const url = `${VAULT_API_BASE}/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${queryParams}`;
    const response = await axios.get(url);
    
    return {
      totalFiles: response.data.pagination?.totalResults || 0,
      files: response.data.results || [],
      folders: response.data.included?.folder || {}
    };
  } catch (error) {
    throw new Error(`Failed to get vault files: ${error.message}`);
  }
}

async function searchVaultFiles(vaultId, query, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('q', query);
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.latestOnly !== undefined) queryParams.append('option[latestOnly]', params.latestOnly);
    
    const url = `${VAULT_API_BASE}/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${queryParams}`;
    const response = await axios.get(url);
    
    return {
      searchQuery: query,
      totalResults: response.data.pagination?.totalResults || 0,
      files: response.data.results || [],
      folders: response.data.included?.folder || {}
    };
  } catch (error) {
    throw new Error(`Failed to search vault files: ${error.message}`);
  }
}

async function getVaultFilesByUser(vaultId, userName, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append(`filter[CreateUserName]`, userName);
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.latestOnly !== undefined) queryParams.append('option[latestOnly]', params.latestOnly);
    
    const url = `${VAULT_API_BASE}/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${queryParams}`;
    const response = await axios.get(url);
    
    return {
      userName: userName,
      totalFiles: response.data.pagination?.totalResults || 0,
      files: response.data.results || [],
      folders: response.data.included?.folder || {}
    };
  } catch (error) {
    throw new Error(`Failed to get files by user: ${error.message}`);
  }
}

async function getVaultFilesByState(vaultId, state, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append(`filter[State]`, state);
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.latestOnly !== undefined) queryParams.append('option[latestOnly]', params.latestOnly);
    
    const url = `${VAULT_API_BASE}/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${queryParams}`;
    const response = await axios.get(url);
    
    return {
      state: state,
      totalFiles: response.data.pagination?.totalResults || 0,
      files: response.data.results || [],
      folders: response.data.included?.folder || {}
    };
  } catch (error) {
    throw new Error(`Failed to get files by state: ${error.message}`);
  }
}

async function getVaultFilesByCategory(vaultId, category, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append(`filter[CategoryName]`, category);
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.latestOnly !== undefined) queryParams.append('option[latestOnly]', params.latestOnly);
    
    const url = `${VAULT_API_BASE}/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${queryParams}`;
    const response = await axios.get(url);
    
    return {
      category: category,
      totalFiles: response.data.pagination?.totalResults || 0,
      files: response.data.results || [],
      folders: response.data.included?.folder || {}
    };
  } catch (error) {
    throw new Error(`Failed to get files by category: ${error.message}`);
  }
}

app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'vault-mcp-server' });
});

app.listen(PORT, () => {
  console.log(`Vault MCP Server running on http://localhost:${PORT}`);
  console.log(`Connected to Vault Mock API at ${VAULT_API_BASE}`);
});
