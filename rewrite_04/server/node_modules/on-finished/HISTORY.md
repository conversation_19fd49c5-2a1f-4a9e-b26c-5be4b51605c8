2.4.1 / 2022-02-22
==================

  * Fix error on early async hooks implementations

2.4.0 / 2022-02-21
==================

  * Prevent loss of async hooks context

2.3.0 / 2015-05-26
==================

  * Add defined behavior for HTTP `CONNECT` requests
  * Add defined behavior for HTTP `Upgrade` requests
  * deps: ee-first@1.1.1

2.2.1 / 2015-04-22
==================

  * Fix `isFinished(req)` when data buffered

2.2.0 / 2014-12-22
==================

  * Add message object to callback arguments

2.1.1 / 2014-10-22
==================

  * Fix handling of pipelined requests

2.1.0 / 2014-08-16
==================

  * Check if `socket` is detached
  * Return `undefined` for `isFinished` if state unknown

2.0.0 / 2014-08-16
==================

  * Add `isFinished` function
  * Move to `jshttp` organization
  * Remove support for plain socket argument
  * Rename to `on-finished`
  * Support both `req` and `res` as arguments
  * deps: ee-first@1.0.5

1.2.2 / 2014-06-10
==================

  * Reduce listeners added to emitters
    - avoids "event emitter leak" warnings when used multiple times on same request

1.2.1 / 2014-06-08
==================

  * Fix returned value when already finished

1.2.0 / 2014-06-05
==================

  * Call callback when called on already-finished socket

1.1.4 / 2014-05-27
==================

  * Support node.js 0.8

1.1.3 / 2014-04-30
==================

  * Make sure errors passed as instanceof `Error`

1.1.2 / 2014-04-18
==================

  * Default the `socket` to passed-in object

1.1.1 / 2014-01-16
==================

  * Rename module to `finished`

1.1.0 / 2013-12-25
==================

  * Call callback when called on already-errored socket

1.0.1 / 2013-12-20
==================

  * Actually pass the error to the callback

1.0.0 / 2013-12-20
==================

  * Initial release
