using System.Text.Json;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddHttpClient("default").ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
    };
});
builder.Services.AddCors();

var app = builder.Build();

app.UseCors(policy => policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

const string AI_API_URL = "https://api.epicai.fun/v1/chat/completions";
const string AI_API_KEY = "ecs-00";
const string MCP_SERVER_URL = "http://localhost:5002";

app.MapPost("/ask", async (AskRequest request, IHttpClientFactory httpClientFactory) =>
{
    var httpClient = httpClientFactory.CreateClient("default");
    
    if (string.IsNullOrEmpty(request.Message))
    {
        return Results.BadRequest(new { error = "Message is required" });
    }
    
    try
    {
        var toolsResponse = await httpClient.GetStringAsync($"{MCP_SERVER_URL}/tools");
        var tools = JsonSerializer.Deserialize<Tool[]>(toolsResponse) ?? Array.Empty<Tool>();

        var toolSelectionPrompt = $@"
User message: {request.Message}

Available vault tools:
{string.Join("\n", tools.Select(t => $"- {t.Name}: {t.Description}"))}

Determine which tools are needed to answer the user's vault-related question.
Respond with ONLY a JSON array of tool names. Examples:
- For ""list my files"": [""listVaultFiles""]
- For ""search for dwg files"": [""searchVaultFiles""]
- For ""files by TestAccount"": [""getVaultFilesByUser""]
- For ""released files"": [""getVaultFilesByState""]
- For ""documentation files"": [""getVaultFilesByCategory""]

JSON array only:";

        var toolSelectionResponse = await CallAI(httpClient, toolSelectionPrompt);

        var toolResults = new List<object>();
        try
        {
            var selectedTools = JsonSerializer.Deserialize<string[]>(toolSelectionResponse.Trim()) ?? Array.Empty<string>();

            foreach (var toolName in selectedTools)
            {
                var toolParams = DetermineToolParams(toolName, request.Message);
                
                var toolRequest = new { tool = toolName, @params = toolParams };
                var toolJson = JsonSerializer.Serialize(toolRequest);
                var content = new StringContent(toolJson, Encoding.UTF8, "application/json");

                var toolResponse = await httpClient.PostAsync($"{MCP_SERVER_URL}/tool", content);
                var toolResult = await toolResponse.Content.ReadAsStringAsync();
                var deserializedResult = JsonSerializer.Deserialize<object>(toolResult);
                if (deserializedResult != null)
                    toolResults.Add(deserializedResult);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Tool execution error: {ex.Message}");
        }

        var finalPrompt = $@"
User asked about their vault: {request.Message}

Vault data from tools: {JsonSerializer.Serialize(toolResults)}

Based on the vault data, provide a helpful and natural response about the user's vault files. 
Be specific about file names, states, users, and other relevant details from the data.
If no data is available, explain that the vault might be empty or the query didn't match any files.
";

        var finalResponse = await CallAI(httpClient, finalPrompt);

        return Results.Ok(new { reply = finalResponse });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

Dictionary<string, object> DetermineToolParams(string toolName, string userMessage)
{
    var parameters = new Dictionary<string, object>
    {
        ["vaultId"] = "117",
        ["limit"] = 10
    };

    var lowerMessage = userMessage.ToLower();

    switch (toolName)
    {
        case "searchVaultFiles":
            parameters["query"] = ExtractSearchQuery(userMessage);
            break;
            
        case "getVaultFilesByUser":
            parameters["userName"] = ExtractUserName(userMessage);
            break;
            
        case "getVaultFilesByState":
            parameters["state"] = ExtractState(userMessage);
            break;
            
        case "getVaultFilesByCategory":
            parameters["category"] = ExtractCategory(userMessage);
            break;
    }

    if (lowerMessage.Contains("released") || lowerMessage.Contains("final"))
    {
        parameters["releasedFilesOnly"] = true;
    }

    return parameters;
}

string ExtractSearchQuery(string message)
{
    var lowerMessage = message.ToLower();
    
    var patterns = new[]
    {
        @"search.*?for\s+(.+)",
        @"find\s+(.+)",
        @"look.*?for\s+(.+)",
        @"show.*?me\s+(.+)",
        @"files.*?with\s+(.+)",
        @"(.+)\s+files"
    };

    foreach (var pattern in patterns)
    {
        var match = System.Text.RegularExpressions.Regex.Match(lowerMessage, pattern);
        if (match.Success && match.Groups.Count > 1)
        {
            return match.Groups[1].Value.Trim();
        }
    }

    return message;
}

string ExtractUserName(string message)
{
    var lowerMessage = message.ToLower();
    
    if (lowerMessage.Contains("testaccount")) return "TestAccount";
    if (lowerMessage.Contains("anotheruser")) return "AnotherUser";
    if (lowerMessage.Contains("docuser")) return "DocUser";
    
    var patterns = new[]
    {
        @"by\s+(\w+)",
        @"from\s+(\w+)",
        @"user\s+(\w+)",
        @"created.*?by\s+(\w+)"
    };

    foreach (var pattern in patterns)
    {
        var match = System.Text.RegularExpressions.Regex.Match(lowerMessage, pattern);
        if (match.Success && match.Groups.Count > 1)
        {
            return match.Groups[1].Value;
        }
    }

    return "TestAccount";
}

string ExtractState(string message)
{
    var lowerMessage = message.ToLower();
    
    if (lowerMessage.Contains("released")) return "Released";
    if (lowerMessage.Contains("work in progress") || lowerMessage.Contains("wip")) return "Work in Progress";
    
    return "Released";
}

string ExtractCategory(string message)
{
    var lowerMessage = message.ToLower();
    
    if (lowerMessage.Contains("documentation") || lowerMessage.Contains("doc")) return "Documentation";
    if (lowerMessage.Contains("base")) return "Base";
    
    return "Base";
}

async Task<string> CallAI(HttpClient httpClient, string prompt)
{
    try
    {
        var aiRequest = new
        {
            model = "gpt-4.1-nano",
            messages = new[]
            {
                new { role = "user", content = prompt }
            },
            temperature = 0.1,
            max_tokens = 1000,
            stream = false
        };

        var json = JsonSerializer.Serialize(aiRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        httpClient.DefaultRequestHeaders.Clear();
        httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {AI_API_KEY}");

        var response = await httpClient.PostAsync(AI_API_URL, content);
        var responseJson = await response.Content.ReadAsStringAsync();

        var aiResponse = JsonSerializer.Deserialize<AiResponse>(responseJson);
        return aiResponse?.Choices?[0]?.Message?.Content ?? "No response from AI";
    }
    catch (Exception ex)
    {
        Console.WriteLine($"AI Call Error: {ex.Message}");
        return "Sorry, I encountered an error while processing your request.";
    }
}

app.MapGet("/health", () => new { status = "ok", service = "vault-mcp-client" });

app.Run("http://localhost:5099");

public record AskRequest(string Message);
public record Tool(string Name, string Description);
public record AiResponse(Choice[] Choices);
public record Choice(Message Message);
public record Message(string Content);
